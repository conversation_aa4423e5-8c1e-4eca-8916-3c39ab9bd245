{"name": "frontend-2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:alt": "vite --port 3005", "dev:custom": "node start-dev.js", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@mohit-kumar/common-zod-all": "^1.0.0", "axios": "^1.7.3", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-loader-spinner": "^6.1.6", "react-router-dom": "^6.26.0"}, "devDependencies": {"@eslint/js": "^9.8.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.8.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "tailwindcss": "^3.4.9", "typescript": "^5.5.3", "typescript-eslint": "^8.0.0", "vite": "^6.3.5"}}