@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for a more professional UI */
@layer utilities {
  /* Blob animation for background elements */
  .animate-blob {
    animation: blob 7s infinite;
  }

  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }

  /* Animation delays */
  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Fade in animation */
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out forwards;
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  /* Fade in up animation for scroll effects */
  .animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Slow pulse animation */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Gradient text animation */
  .animate-gradient-text {
    background-size: 200% 200%;
    animation: gradientText 5s ease infinite;
  }

  @keyframes gradientText {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Shine effect for cards */
  .hover-shine {
    position: relative;
    overflow: hidden;
  }

  .hover-shine::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 100%
    );
    transform: skewX(-25deg);
    transition: all 0.75s;
  }

  .hover-shine:hover::before {
    left: 125%;
  }
}