<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a237e;
        }
        button {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #303f9f;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BlogNest Test Page</h1>
        <p>This is a test page to verify that the server is working correctly.</p>
        
        <h2>Test Content Generation</h2>
        <input type="text" id="topic" placeholder="Enter a topic" style="padding: 8px; width: 70%; margin-right: 10px;">
        <button onclick="generateContent()">Generate Content</button>
        
        <div class="result" id="result">
            Content will appear here...
        </div>
    </div>

    <script>
        function generateContent() {
            const topic = document.getElementById('topic').value;
            if (!topic) {
                alert('Please enter a topic');
                return;
            }
            
            document.getElementById('result').innerHTML = 'Generating content...';
            
            // Mock content generation
            setTimeout(() => {
                const content = `<h3>${topic}</h3>
                <p>${topic} is a fascinating subject that encompasses multiple dimensions and perspectives. When exploring ${topic}, it's important to consider both theoretical foundations and practical applications.</p>
                <p>The field has evolved significantly over time, adapting to changing technologies, methodologies, and best practices.</p>
                <p>For those looking to gain expertise in ${topic}, a combination of theoretical study and hands-on practice is typically recommended.</p>`;
                
                document.getElementById('result').innerHTML = content;
            }, 1000);
        }
    </script>
</body>
</html>
