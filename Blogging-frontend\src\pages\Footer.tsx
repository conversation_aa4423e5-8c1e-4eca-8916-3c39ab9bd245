const Footer = () => {
  return (
    <footer className="bg-gradient-to-b from-indigo-900 to-indigo-950 py-10 text-center lg:text-left">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="text-white">
            <div className="flex items-center justify-center md:justify-start mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg shadow-md flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-900" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
              </div>
              <span className="text-3xl font-bold font-serif bg-gradient-to-r from-slate-50 to-slate-200 text-transparent bg-clip-text">BlogNest</span>
            </div>
            <p className="text-slate-300 font-serif max-w-xs mx-auto md:mx-0">
              An elegant platform for writers and readers to connect, share ideas, and explore new literary perspectives.
            </p>
          </div>

          <div className="text-white">
            <h3 className="text-xl font-serif font-bold mb-4 text-slate-200">Connect With Us</h3>
            <div className="flex space-x-4 justify-center md:justify-start mb-4">
              <a
                href="https://github.com/Harshmishra001"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-indigo-800 p-2 rounded-full hover:bg-indigo-700 transition-colors duration-200"
                title="GitHub"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </a>

              <a
                href="https://www.linkedin.com/in/harsh-mishra001/"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-indigo-800 p-2 rounded-full hover:bg-indigo-700 transition-colors duration-200"
                title="LinkedIn"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                </svg>
              </a>

              <a
                href="mailto:<EMAIL>"
                className="bg-indigo-800 p-2 rounded-full hover:bg-indigo-700 transition-colors duration-200"
                title="Email"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M0 3v18h24v-18h-24zm21.518 2l-9.518 7.713-9.518-7.713h19.036zm-19.518 14v-11.817l10 8.104 10-8.104v11.817h-20z"/>
                </svg>
              </a>
            </div>

            <p className="text-slate-300 font-serif mb-4">
              Made with ❤️ by <span className="bg-gradient-to-r from-purple-200 to-indigo-200 text-transparent bg-clip-text font-semibold">Harsh Kumar Mishra</span>
            </p>

            <div className="text-slate-300 font-serif space-y-2">
              <p className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-300" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                <a href="mailto:<EMAIL>" className="hover:text-indigo-300 transition-colors duration-200">
                  <EMAIL>
                </a>
              </p>
              <p className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                <a href="https://github.com/Harshmishra001" target="_blank" rel="noopener noreferrer" className="hover:text-indigo-300 transition-colors duration-200">
                  github.com/Harshmishra001
                </a>
              </p>
              <p className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                  <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                </svg>
                <a href="https://www.linkedin.com/in/harsh-mishra001/" target="_blank" rel="noopener noreferrer" className="hover:text-indigo-300 transition-colors duration-200">
                  linkedin.com/in/harsh-mishra001
                </a>
              </p>
            </div>
          </div>
        </div>

        <div className="border-t border-indigo-800 mt-8 pt-8 text-center">
          <p className="text-slate-300 font-serif">
            &copy; {new Date().getFullYear()} BlogNest by Harsh Kumar Mishra. All rights reserved.
          </p>
          <p className="text-slate-400 text-sm mt-2 font-serif">
            <a href="https://www.linkedin.com/in/harsh-mishra001/" target="_blank" rel="noopener noreferrer" className="hover:text-indigo-300 transition-colors duration-200">LinkedIn</a> |
            <a href="https://github.com/Harshmishra001" target="_blank" rel="noopener noreferrer" className="hover:text-indigo-300 transition-colors duration-200 mx-2">GitHub</a> |
            <a href="mailto:<EMAIL>" className="hover:text-indigo-300 transition-colors duration-200">Email</a>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
